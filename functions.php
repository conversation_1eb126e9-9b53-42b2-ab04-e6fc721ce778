<?php
/**
 * Histudy Child Theme Functions
 */

add_action('wp_enqueue_scripts', 'histudy_child_enqueue_styles');
function histudy_child_enqueue_styles() {
    wp_enqueue_style('parent-style', get_template_directory_uri() . '/style.css', null, filemtime(get_template_directory() . '/style.css'));
    wp_enqueue_style('histudy-child-style', get_stylesheet_uri(), null, filemtime(get_stylesheet_directory() . '/style.css'));
}

// Disable auto updates
add_filter('auto_update_theme', '__return_false');
add_filter('auto_update_plugin', '__return_false');

remove_action('admin_init', '_maybe_update_core');
remove_action('admin_init', '_maybe_update_plugins');
remove_action('admin_init', '_maybe_update_themes');

/**
 * Hero shortcode - compatible with parent theme
 * Uses output buffering to ensure proper rendering with Elementor
 */
function hero_shortcode() {
    ob_start();
    get_template_part('template-parts/components/hero');
    return ob_get_clean();
}
add_shortcode('hero', 'hero_shortcode');

// Scripts enqueuing function
function asil_enqueue_scripts() {
    // Hero carousel script
    $hero_carousel_file = get_stylesheet_directory() . '/assets/js/hero-swiper.js';
    wp_enqueue_script(
        'asil-hero-carousel',
        get_stylesheet_directory_uri() . '/assets/js/hero-swiper.js',
        array('jquery'),
        (file_exists($hero_carousel_file) ? filemtime($hero_carousel_file) : '1.0'),
        true
    );

    // Hero educational effects script
    $hero_educational_file = get_stylesheet_directory() . '/assets/js/hero-educational.js';
    wp_enqueue_script(
        'asil-hero-educational',
        get_stylesheet_directory_uri() . '/assets/js/hero-educational.js',
        array(),
        (file_exists($hero_educational_file) ? filemtime($hero_educational_file) : '1.0'),
        true
    );
}
add_action('wp_enqueue_scripts', 'asil_enqueue_scripts');