/**
 * Hero Educational Interactive Effects - Fun Learning Games
 */
(function () {
    document.addEventListener('DOMContentLoaded', function () {
        initEducationalHero();
    });

    function initEducationalHero() {
        const heroTitle = document.querySelector('.hero-content h1');
        const ctaButtons = document.querySelectorAll('.hero-content .rbt-btn, .hero-cta-mobile .rbt-btn');

        if (!heroTitle) return;

        // Initialize educational title effects
        initEducationalTitle(heroTitle);

        // Initialize fun CTA effects
        ctaButtons.forEach(button => initFunCTA(button));

        // Start cheerful animations
        startCheerfulAnimations(heroTitle, ctaButtons);

        // Add learning game mechanics
        initLearningGame(heroTitle);
    }

    function initEducationalTitle(title) {
        const originalText = title.textContent;

        // Turkish-English word pairs for the title
        const wordTranslations = {
            'ASIL': 'REAL',
            'İNGİLİZCE': 'ENGLISH',
            'ÖĞRENME': 'LEARNING',
            'YOLCULUĞUNUZ': 'JOURNEY',
            'BURADA': 'HERE',
            'BAŞLIYOR': 'BEGINS'
        };

        // Wrap words with translations
        wrapWordsWithTranslations(title, wordTranslations);



        // Add score display
        const scoreDisplay = document.createElement('div');
        scoreDisplay.className = 'language-score';
        scoreDisplay.innerHTML = '🇹🇷 ➜ 🇬🇧 Skor: 0/6';
        scoreDisplay.style.cssText = `
            position: absolute;
            top: -25px;
            right: 0px;
            background: linear-gradient(45deg, #e74c3c, #3498db);
            color: white;
            padding: 3px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease;
            z-index: 20;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.6);
            white-space: nowrap;
            max-width: 120px;
            text-align: center;
            line-height: 1.2;
        `;
        title.appendChild(scoreDisplay);

        // Add click event for Turkish-English learning game
        title.addEventListener('click', function() {
            playLanguageLearningGame(title, wordTranslations);
        });

        // Add individual word click events
        addWordClickEvents(title, wordTranslations);
    }

    function wrapWordsWithTranslations(element, translations) {
        const text = element.textContent;
        const words = text.split(' ');
        element.innerHTML = words.map((word, index) => {
            const cleanWord = word.replace(/[^\w\sÇĞıİÖŞÜçğıöşü]/gi, '');
            const translation = translations[cleanWord] || '';
            return `<span class="edu-word"
                        data-word-index="${index}"
                        data-turkish="${cleanWord}"
                        data-english="${translation}"
                        title="Çeviri için tıkla: ${translation}">${word}</span>`;
        }).join(' ');
    }

    function addWordClickEvents(title, translations) {
        const words = title.querySelectorAll('.edu-word');

        words.forEach(word => {
            word.addEventListener('click', function(e) {
                e.stopPropagation();
                const turkish = this.getAttribute('data-turkish');
                const english = this.getAttribute('data-english');

                if (english) {
                    showWordTranslation(this, turkish, english);
                }
            });
        });
    }

    function showWordTranslation(wordElement, turkish, english) {
        // Toggle between Turkish and English
        const isShowingEnglish = wordElement.classList.contains('showing-english');

        if (isShowingEnglish) {
            // Show Turkish
            wordElement.textContent = turkish;
            wordElement.classList.remove('showing-english');
            wordElement.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
            wordElement.setAttribute('title', `English: ${english}`);
        } else {
            // Show English
            wordElement.textContent = english;
            wordElement.classList.add('showing-english');
            wordElement.style.background = 'linear-gradient(135deg, #27ae60, #2ecc71)';
            wordElement.setAttribute('title', `Türkçe: ${turkish}`);

            // Create learning effect
            createLanguageLearningEffect(wordElement);
        }

        // Update score
        updateLanguageScore(wordElement.closest('h1'));
    }

    function playLanguageLearningGame(title, translations) {
        const words = title.querySelectorAll('.edu-word');
        let currentIndex = 0;

        // Add learning class to show progress
        title.classList.add('learning');

        // Show instructions
        showGameInstructions(title);

        function translateNextWord() {
            if (currentIndex < words.length) {
                const word = words[currentIndex];
                const turkish = word.getAttribute('data-turkish');
                const english = word.getAttribute('data-english');

                if (english) {
                    // Animate word transformation
                    word.style.transform = 'scale(1.2) rotateY(180deg)';

                    setTimeout(() => {
                        word.textContent = english;
                        word.classList.add('learned', 'showing-english');
                        word.style.background = 'linear-gradient(135deg, #3498db, #2980b9)';
                        word.style.transform = 'scale(1.1) rotateY(0deg)';

                        // Create confetti effect
                        createLanguageLearningEffect(word);

                        currentIndex++;
                        setTimeout(translateNextWord, 800);
                    }, 300);
                } else {
                    currentIndex++;
                    setTimeout(translateNextWord, 100);
                }
            } else {
                // Game completed
                setTimeout(() => {
                    celebrateLanguageLearning(title, translations);
                }, 500);
            }
        }

        translateNextWord();
    }

    function createLanguageLearningEffect(element) {
        // Create flag emojis
        const flags = ['🇹🇷', '🇬🇧'];

        flags.forEach((flag, index) => {
            const flagElement = document.createElement('div');
            flagElement.textContent = flag;
            flagElement.style.cssText = `
                position: absolute;
                font-size: 16px;
                animation: flagFloat 1.5s ease-out forwards;
                z-index: 100;
                pointer-events: none;
            `;
            flagElement.style.left = (Math.random() * 50 - 25) + 'px';
            flagElement.style.top = (Math.random() * 30 - 15) + 'px';
            flagElement.style.animationDelay = (index * 0.2) + 's';

            element.appendChild(flagElement);

            setTimeout(() => {
                if (flagElement.parentNode) {
                    flagElement.parentNode.removeChild(flagElement);
                }
            }, 1500);
        });

        // Create confetti
        createConfetti(element);
    }

    function createConfetti(element) {
        const colors = ['#FFD93D', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];

        for (let i = 0; i < 6; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.background = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.animationDelay = Math.random() * 0.3 + 's';

            element.appendChild(confetti);

            // Remove confetti after animation
            setTimeout(() => {
                if (confetti.parentNode) {
                    confetti.parentNode.removeChild(confetti);
                }
            }, 2000);
        }
    }

    function updateLanguageScore(title) {
        const scoreDisplay = title.querySelector('.language-score');
        const englishWords = title.querySelectorAll('.edu-word.showing-english');
        const totalWords = title.querySelectorAll('.edu-word[data-english]').length;

        if (scoreDisplay) {
            scoreDisplay.innerHTML = `🇹🇷 ➜ 🇬🇧 Skor: ${englishWords.length}/${totalWords}`;

            if (englishWords.length === totalWords) {
                scoreDisplay.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                scoreDisplay.style.color = 'white';
                scoreDisplay.innerHTML = `🏆 Mükemmel! ${totalWords}/${totalWords} ✨`;
            }
        }
    }

    function showGameInstructions(title) {
        const instructions = document.createElement('div');
        instructions.innerHTML = '🎮 Türkçe ➜ İngilizce çeviri oyunu başlıyor!';
        instructions.style.cssText = `
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            white-space: nowrap;
            z-index: 100;
            animation: instructionFade 3s ease forwards;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        `;

        title.appendChild(instructions);

        setTimeout(() => {
            if (instructions.parentNode) {
                instructions.parentNode.removeChild(instructions);
            }
        }, 3000);
    }

    function celebrateLanguageLearning(title, translations) {
        // Show completion message
        const celebration = document.createElement('div');
        celebration.innerHTML = '🎉 Tebrikler! İngilizce öğrenme yolculuğunuz başladı! 🇬🇧✨';
        celebration.style.cssText = `
            position: absolute;
            top: -70px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #e74c3c, #27ae60);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            white-space: nowrap;
            z-index: 100;
            animation: celebrationPop 3s ease forwards;
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
        `;

        title.appendChild(celebration);

        // Reset after celebration
        setTimeout(() => {
            title.classList.remove('learning');
            title.querySelectorAll('.edu-word').forEach(word => {
                const turkish = word.getAttribute('data-turkish');
                word.textContent = turkish;
                word.classList.remove('learned', 'showing-english');
                word.style.background = '';
                word.style.transform = '';
            });

            // Reset score
            updateLanguageScore(title);

            if (celebration.parentNode) {
                celebration.parentNode.removeChild(celebration);
            }
        }, 4000);
    }

    function initFunCTA(button) {
        // Add progress indicator
        const progressIndicator = document.createElement('div');
        progressIndicator.className = 'btn-progress';
        button.appendChild(progressIndicator);

        // Add achievement badge
        const badge = document.createElement('div');
        badge.className = 'achievement-badge';
        badge.textContent = '!';
        button.appendChild(badge);

        // Fun click interaction
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Start learning journey animation
            startLearningJourney(button);
        });

        // Cheerful hover effects
        button.addEventListener('mouseenter', function() {
            this.style.animation = 'buttonBounce 0.6s ease';
        });
    }

    function startLearningJourney(button) {
        const originalText = button.textContent;
        const journeySteps = [
            '🚀 Hazırlanıyor...',
            '📚 Dersler yükleniyor...',
            '🎯 Hedefler belirleniyor...',
            '✨ Yolculuk başlıyor!'
        ];

        let stepIndex = 0;

        function nextStep() {
            if (stepIndex < journeySteps.length) {
                button.textContent = journeySteps[stepIndex];
                button.style.background = `linear-gradient(135deg,
                    hsl(${stepIndex * 60}, 70%, 60%),
                    hsl(${(stepIndex + 1) * 60}, 70%, 60%))`;
                stepIndex++;
                setTimeout(nextStep, 800);
            } else {
                // Complete the journey
                button.style.background = 'linear-gradient(135deg, #4ECDC4, #44A08D)';
                button.textContent = '🎉 Başarılı!';

                setTimeout(() => {
                    // Navigate to actual URL
                    window.location.href = button.href;
                }, 1000);
            }
        }

        nextStep();
    }

    function startCheerfulAnimations(title, buttons) {
        // Periodic word highlighting for attention
        setInterval(() => {
            const words = title.querySelectorAll('.edu-word');
            if (words.length > 0 && !title.classList.contains('learning')) {
                const randomWord = words[Math.floor(Math.random() * words.length)];
                randomWord.style.background = 'linear-gradient(135deg, #FFD93D, #FF6B6B)';
                randomWord.style.color = 'white';
                randomWord.style.transform = 'translateY(-2px) scale(1.05)';

                setTimeout(() => {
                    randomWord.style.background = '';
                    randomWord.style.color = '';
                    randomWord.style.transform = '';
                }, 1000);
            }
        }, 4000);

        // Periodic CTA pulse for engagement
        setInterval(() => {
            buttons.forEach(button => {
                if (!button.matches(':hover')) {
                    button.classList.add('pulse-learn');
                    setTimeout(() => {
                        button.classList.remove('pulse-learn');
                    }, 2000);
                }
            });
        }, 8000);
    }

    function initLearningGame(title) {
        let gameScore = 0;

        // Create score display
        const scoreDisplay = document.createElement('div');
        scoreDisplay.className = 'hero-game-score';
        scoreDisplay.style.cssText = `
            position: absolute;
            top: -55px;
            right: 0px;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 4px 10px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 25;
            box-shadow: 0 3px 12px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.7);
            white-space: nowrap;
            max-width: 140px;
            text-align: center;
            line-height: 1.2;
        `;
        title.appendChild(scoreDisplay);

        // Track word learning
        title.addEventListener('click', function() {
            const words = title.querySelectorAll('.edu-word');
            gameScore = words.length;
            scoreDisplay.textContent = `Öğrenilen: ${gameScore} kelime! 🎉`;
            scoreDisplay.classList.add('show');

            setTimeout(() => {
                scoreDisplay.classList.remove('show');
                gameScore = 0;
            }, 4000);
        });
    }

    // Add CSS animations dynamically
    const style = document.createElement('style');
    style.textContent = `
        @keyframes celebrationPop {
            0% { opacity: 0; transform: translateX(-50%) scale(0); }
            50% { opacity: 1; transform: translateX(-50%) scale(1.1); }
            100% { opacity: 1; transform: translateX(-50%) scale(1); }
        }

        .hero-game-score.show {
            opacity: 1 !important;
            transform: translateY(0) scale(1.05) !important;
        }

        /* Mobile responsiveness for both score displays */
        @media (max-width: 768px) {
            .language-score {
                font-size: 8px !important;
                padding: 2px 5px !important;
                top: -20px !important;
                right: 0px !important;
                max-width: 100px !important;
                border-radius: 5px !important;
            }

            .hero-game-score {
                font-size: 8px !important;
                padding: 3px 6px !important;
                top: -45px !important;
                right: 0px !important;
                max-width: 110px !important;
                border-radius: 6px !important;
            }
        }

        @media (max-width: 480px) {
            .language-score {
                font-size: 7px !important;
                padding: 2px 4px !important;
                top: -18px !important;
                right: 0px !important;
                max-width: 85px !important;
                border-radius: 4px !important;
            }

            .hero-game-score {
                font-size: 7px !important;
                padding: 2px 5px !important;
                top: -35px !important;
                right: 0px !important;
                max-width: 95px !important;
                border-radius: 5px !important;
            }
        }
    `;
    document.head.appendChild(style);
})();
